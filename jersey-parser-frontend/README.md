# Jersey Parser Frontend

A modern React/Next.js frontend for the Horse Racing Jersey Parser service. This application provides a beautiful, responsive interface for parsing jersey descriptions and searching through parsed results.

## Features

- 🎨 **Modern UI**: Beautiful gradient design with dark mode support
- 🔍 **Real-time Parsing**: Parse complex jersey descriptions with AI-powered NLP
- 🌈 **Color Recognition**: Supports 160+ colors with visual color swatches
- 🔎 **Advanced Search**: Filter by colors, patterns, and elements
- 📱 **Responsive Design**: Works perfectly on desktop, tablet, and mobile
- ⚡ **Fast & Optimized**: Built with Next.js 15 and modern React patterns
- 🌙 **Dark Mode**: Toggle between light and dark themes
- 🎯 **TypeScript**: Fully typed for better development experience

## Technology Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Backend**: AWS Lambda + API Gateway
- **Database**: PostgreSQL with spaCy NLP

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm, yarn, or pnpm
- Access to the Jersey Parser backend API

### Installation

1. **Install dependencies**:
   ```bash
   npm install
   # or
   yarn install
   # or
   pnpm install
   ```

2. **Configure environment variables**:
   ```bash
   cp env.example .env.local
   ```
   
   Edit `.env.local` and update the API URL:
   ```env
   NEXT_PUBLIC_API_URL=https://your-api-gateway-url.amazonaws.com/prod
   ```

3. **Run the development server**:
   ```bash
   npm run dev
   # or
   yarn dev
   # or
   pnpm dev
   ```

4. **Open your browser**:
   Navigate to [http://localhost:3000](http://localhost:3000)

## Usage

### Parsing Jerseys

1. Navigate to the "Parse Jersey" tab
2. Enter a jersey description like:
   - "red body with white stripes, blue sleeves with gold stars"
   - "navy blue base, yellow chevrons on sleeves, white cap with red peak"
3. Click "Parse Jersey" to see the structured results
4. View extracted colors, patterns, and elements with confidence scores

### Searching Jerseys

1. Navigate to the "Search Jerseys" tab
2. Use filters to search by:
   - General text query
   - Specific colors
   - Pattern types
3. View paginated results with visual indicators

## API Integration

The frontend connects to your AWS Lambda backend through Next.js API routes:

- `POST /api/parse` - Parse jersey descriptions
- `GET /api/search` - Search parsed jerseys

These routes proxy requests to your AWS API Gateway endpoints.

## Project Structure

```
src/
├── app/
│   ├── api/           # Next.js API routes
│   ├── globals.css    # Global styles
│   ├── layout.tsx     # Root layout
│   └── page.tsx       # Home page
├── components/
│   ├── Header.tsx     # Navigation header
│   ├── Footer.tsx     # Site footer
│   ├── JerseyParser.tsx   # Parsing interface
│   └── JerseySearch.tsx   # Search interface
└── types/
    └── jersey.ts      # TypeScript definitions
```

## Customization

### Styling

The app uses Tailwind CSS for styling. You can customize:

- Colors in `tailwind.config.js`
- Global styles in `src/app/globals.css`
- Component-specific styles in individual components

### API Configuration

Update the API endpoints in:
- `src/app/api/parse/route.ts`
- `src/app/api/search/route.ts`

## Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Set environment variables in Vercel dashboard
4. Deploy automatically on push

### Other Platforms

```bash
# Build the application
npm run build

# Start production server
npm run start
```

## Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

### Code Quality

The project includes:
- ESLint for code linting
- TypeScript for type safety
- Tailwind CSS for consistent styling

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is part of the Jersey Parser service suite.
