'use client'

import { useState } from 'react'
import { ParsedJersey } from '@/types/jersey'

export function JerseyParser() {
  const [description, setDescription] = useState('')
  const [parsedResult, setParsedResult] = useState<ParsedJersey | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const exampleDescriptions = [
    "red body with white stripes, blue sleeves with gold stars",
    "navy blue base, yellow chevrons on sleeves, white cap with red peak",
    "emerald green shirt with silver hoops, black armbands",
    "purple and gold quarters, white sleeves, maroon cap"
  ]

  const handleParse = async () => {
    if (!description.trim()) return

    setIsLoading(true)
    setError(null)
    
    try {
      // Call AWS API Gateway directly
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'https://xmq27pyhl2.execute-api.ap-southeast-2.amazonaws.com/prod/'
      const response = await fetch(`${apiUrl}jerseys/parse`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ description }),
      })

      if (!response.ok) {
        throw new Error('Failed to parse jersey description')
      }

      const result = await response.json()
      setParsedResult(result)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  const handleExampleClick = (example: string) => {
    setDescription(example)
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
      <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Parse Jersey Description</h2>
      
      {/* Input Section */}
      <div className="space-y-4 mb-6">
        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Jersey Description
          </label>
          <textarea
            id="description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="Enter a detailed jersey description (e.g., 'red body with white stripes, blue sleeves with gold stars')"
            className="w-full h-32 px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white resize-none"
          />
        </div>
        
        <div className="flex flex-wrap gap-2">
          <span className="text-sm text-gray-500 dark:text-gray-400">Try these examples:</span>
          {exampleDescriptions.map((example, index) => (
            <button
              key={index}
              onClick={() => handleExampleClick(example)}
              className="text-xs px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded-full hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors"
            >
              {example.length > 40 ? `${example.substring(0, 40)}...` : example}
            </button>
          ))}
        </div>
        
        <button
          onClick={handleParse}
          disabled={!description.trim() || isLoading}
          className="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 px-6 rounded-lg font-medium hover:from-blue-600 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center justify-center"
        >
          {isLoading ? (
            <>
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Parsing...
            </>
          ) : (
            'Parse Jersey'
          )}
        </button>
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-red-700 dark:text-red-300">{error}</span>
          </div>
        </div>
      )}

      {/* Results Display */}
      {parsedResult && (
        <div className="space-y-6">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Parsed Results</h3>
          
          {/* Colors */}
          {parsedResult.colors && parsedResult.colors.length > 0 && (
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 dark:text-white mb-3">Colors Detected</h4>
              <div className="flex flex-wrap gap-2">
                {parsedResult.colors.map((color, index) => (
                  <div key={index} className="flex items-center space-x-2 bg-white dark:bg-gray-600 px-3 py-2 rounded-lg">
                    <div 
                      className="w-4 h-4 rounded-full border border-gray-300 dark:border-gray-500"
                      style={{ backgroundColor: color.hex_code }}
                    ></div>
                    <span className="text-sm font-medium text-gray-900 dark:text-white">{color.name}</span>
                    <span className="text-xs text-gray-500 dark:text-gray-400">{color.hex_code}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Elements */}
          {parsedResult.elements && parsedResult.elements.length > 0 && (
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 dark:text-white mb-3">Elements Detected</h4>
              <div className="flex flex-wrap gap-2">
                {parsedResult.elements.map((element, index) => (
                  <span key={index} className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-1 rounded-full text-sm font-medium">
                    {element.name}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Patterns */}
          {parsedResult.patterns && parsedResult.patterns.length > 0 && (
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 dark:text-white mb-3">Patterns Detected</h4>
              <div className="flex flex-wrap gap-2">
                {parsedResult.patterns.map((pattern, index) => (
                  <span key={index} className="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-3 py-1 rounded-full text-sm font-medium">
                    {pattern.description}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Confidence Score */}
          {parsedResult.confidence && (
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 dark:text-white mb-3">Confidence Score</h4>
              <div className="flex items-center space-x-3">
                <div className="flex-1 bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                  <div 
                    className="bg-gradient-to-r from-green-400 to-blue-500 h-2 rounded-full transition-all duration-500"
                    style={{ width: `${parsedResult.confidence * 100}%` }}
                  ></div>
                </div>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {Math.round(parsedResult.confidence * 100)}%
                </span>
              </div>
            </div>
          )}

          {/* JSON Output */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 dark:text-white mb-3">Raw JSON Output</h4>
            <pre className="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto">
              {JSON.stringify(parsedResult, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  )
}
