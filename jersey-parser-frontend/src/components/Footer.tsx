export function Footer() {
  return (
    <footer className="bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 mt-16">
      <div className="container mx-auto px-4 py-8">
        <div className="grid md:grid-cols-3 gap-8">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Jersey Parser</h3>
            <p className="text-gray-600 dark:text-gray-300 text-sm">
              Advanced AI-powered parsing for horse racing jersey descriptions. 
              Built with Next.js, React, and powered by spaCy NLP.
            </p>
          </div>
          
          <div>
            <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-4">Features</h4>
            <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
              <li>• 160+ Color Recognition</li>
              <li>• Pattern & Element Detection</li>
              <li>• Fuzzy Matching & Synonyms</li>
              <li>• Real-time Parsing</li>
            </ul>
          </div>
          
          <div>
            <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-4">Technology</h4>
            <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
              <li>• Next.js & React</li>
              <li>• AWS Lambda & API Gateway</li>
              <li>• PostgreSQL Database</li>
              <li>• spaCy NLP Engine</li>
            </ul>
          </div>
        </div>
        
        <div className="border-t border-gray-200 dark:border-gray-700 mt-8 pt-8 text-center">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            © 2025 Jersey Parser. Built with ❤️ for horse racing enthusiasts.
          </p>
        </div>
      </div>
    </footer>
  )
}
