'use client'

import { useState } from 'react'
import { ParsedJersey } from '@/types/jersey'

export function JerseySearch() {
  const [searchQuery, setSearchQuery] = useState('')
  const [colorFilter, setColorFilter] = useState('')
  const [patternFilter, setPatternFilter] = useState('')
  const [results, setResults] = useState<ParsedJersey[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleSearch = async () => {
    setIsLoading(true)
    setError(null)
    
    try {
      const params = new URLSearchParams()
      if (searchQuery) params.append('q', searchQuery)
      if (colorFilter) params.append('color', colorFilter)
      if (patternFilter) params.append('pattern', patternFilter)

      // Replace with your actual API endpoint
      // Call AWS API Gateway directly
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'https://xmq27pyhl2.execute-api.ap-southeast-2.amazonaws.com/prod/'
      const response = await fetch(`${apiUrl}jerseys/search?${params.toString()}`)

      if (!response.ok) {
        throw new Error('Failed to search jerseys')
      }

      const data = await response.json()
      setResults(data.results || [])
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  const clearFilters = () => {
    setSearchQuery('')
    setColorFilter('')
    setPatternFilter('')
    setResults([])
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
      <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Search Jerseys</h2>
      
      {/* Search Filters */}
      <div className="grid md:grid-cols-3 gap-4 mb-6">
        <div>
          <label htmlFor="search" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Search Query
          </label>
          <input
            id="search"
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search descriptions..."
            className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
          />
        </div>
        
        <div>
          <label htmlFor="color" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Color Filter
          </label>
          <select
            id="color"
            value={colorFilter}
            onChange={(e) => setColorFilter(e.target.value)}
            className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
          >
            <option value="">All Colors</option>
            <option value="red">Red</option>
            <option value="blue">Blue</option>
            <option value="green">Green</option>
            <option value="yellow">Yellow</option>
            <option value="purple">Purple</option>
            <option value="orange">Orange</option>
            <option value="pink">Pink</option>
            <option value="black">Black</option>
            <option value="white">White</option>
            <option value="gray">Gray</option>
          </select>
        </div>
        
        <div>
          <label htmlFor="pattern" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Pattern Filter
          </label>
          <select
            id="pattern"
            value={patternFilter}
            onChange={(e) => setPatternFilter(e.target.value)}
            className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
          >
            <option value="">All Patterns</option>
            <option value="stripes">Stripes</option>
            <option value="spots">Spots</option>
            <option value="stars">Stars</option>
            <option value="diamonds">Diamonds</option>
            <option value="chevrons">Chevrons</option>
            <option value="hoops">Hoops</option>
            <option value="checks">Checks</option>
            <option value="solid">Solid</option>
          </select>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-wrap gap-3 mb-6">
        <button
          onClick={handleSearch}
          disabled={isLoading}
          className="bg-gradient-to-r from-blue-500 to-purple-600 text-white py-2 px-6 rounded-lg font-medium hover:from-blue-600 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center"
        >
          {isLoading ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Searching...
            </>
          ) : (
            <>
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
              Search
            </>
          )}
        </button>
        
        <button
          onClick={clearFilters}
          className="bg-gray-500 text-white py-2 px-6 rounded-lg font-medium hover:bg-gray-600 transition-colors"
        >
          Clear Filters
        </button>
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-red-700 dark:text-red-300">{error}</span>
          </div>
        </div>
      )}

      {/* Results */}
      {results.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
            Search Results ({results.length})
          </h3>
          
          <div className="grid gap-4">
            {results.map((jersey, index) => (
              <div key={index} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                <div className="flex justify-between items-start mb-3">
                  <h4 className="font-medium text-gray-900 dark:text-white">
                    Jersey #{jersey.id || index + 1}
                  </h4>
                  {jersey.confidence && (
                    <span className="text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded-full">
                      {Math.round(jersey.confidence * 100)}% confidence
                    </span>
                  )}
                </div>
                
                <p className="text-gray-600 dark:text-gray-300 mb-3 text-sm">
                  {jersey.original_description || 'No description available'}
                </p>
                
                <div className="grid md:grid-cols-3 gap-4">
                  {/* Colors */}
                  {jersey.colors && jersey.colors.length > 0 && (
                    <div>
                      <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Colors</h5>
                      <div className="flex flex-wrap gap-1">
                        {jersey.colors.slice(0, 3).map((color, colorIndex) => (
                          <div key={colorIndex} className="flex items-center space-x-1 bg-white dark:bg-gray-600 px-2 py-1 rounded text-xs">
                            <div 
                              className="w-3 h-3 rounded-full border border-gray-300"
                              style={{ backgroundColor: color.hex_code }}
                            ></div>
                            <span className="text-gray-900 dark:text-white">{color.name}</span>
                          </div>
                        ))}
                        {jersey.colors.length > 3 && (
                          <span className="text-xs text-gray-500 dark:text-gray-400 px-2 py-1">
                            +{jersey.colors.length - 3} more
                          </span>
                        )}
                      </div>
                    </div>
                  )}
                  
                  {/* Elements */}
                  {jersey.elements && jersey.elements.length > 0 && (
                    <div>
                      <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Elements</h5>
                      <div className="flex flex-wrap gap-1">
                        {jersey.elements.slice(0, 3).map((element, elementIndex) => (
                          <span key={elementIndex} className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded text-xs">
                            {element.name}
                          </span>
                        ))}
                        {jersey.elements.length > 3 && (
                          <span className="text-xs text-gray-500 dark:text-gray-400 px-2 py-1">
                            +{jersey.elements.length - 3} more
                          </span>
                        )}
                      </div>
                    </div>
                  )}
                  
                  {/* Patterns */}
                  {jersey.patterns && jersey.patterns.length > 0 && (
                    <div>
                      <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Patterns</h5>
                      <div className="flex flex-wrap gap-1">
                        {jersey.patterns.slice(0, 3).map((pattern, patternIndex) => (
                          <span key={patternIndex} className="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-2 py-1 rounded text-xs">
                            {pattern.description}
                          </span>
                        ))}
                        {jersey.patterns.length > 3 && (
                          <span className="text-xs text-gray-500 dark:text-gray-400 px-2 py-1">
                            +{jersey.patterns.length - 3} more
                          </span>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Empty State */}
      {!isLoading && results.length === 0 && (searchQuery || colorFilter || patternFilter) && (
        <div className="text-center py-8">
          <svg className="w-16 h-16 text-gray-400 dark:text-gray-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No results found</h3>
          <p className="text-gray-500 dark:text-gray-400">Try adjusting your search criteria or clearing the filters.</p>
        </div>
      )}
    </div>
  )
}
