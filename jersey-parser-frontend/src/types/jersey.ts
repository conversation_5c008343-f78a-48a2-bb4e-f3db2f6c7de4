export interface Color {
  id?: number
  name: string
  hex_code: string
}

export interface Element {
  id?: number
  name: string
}

export interface Pattern {
  id?: number
  description: string
  element_type_id?: number
}

export interface ParsedJersey {
  id?: string
  original_description?: string
  colors: Color[]
  elements: Element[]
  patterns: Pattern[]
  confidence?: number
  created_at?: string
  updated_at?: string
}

export interface ParseRequest {
  description: string
}

export interface ParseResponse {
  success: boolean
  data?: ParsedJersey
  error?: string
}

export interface SearchRequest {
  q?: string
  color?: string
  pattern?: string
  element?: string
  limit?: number
  offset?: number
}

export interface SearchResponse {
  success: boolean
  results?: ParsedJersey[]
  total?: number
  error?: string
}
