#!/usr/bin/env python3
import aws_cdk as cdk
from stacks.jersey_parser_stack import JerseyParserStack
from stacks.frontend_stack import FrontendStack

app = cdk.App()

# Deploy backend stack first
backend_stack = JerseyParserStack(app, "JerseyParserStack",
    env=cdk.Environment(
        account=app.node.try_get_context("account"),
        region=app.node.try_get_context("region") or "ap-southeast-2"
    )
)

# Deploy frontend stack with reference to backend API
frontend_stack = FrontendStack(app, "JerseyParserFrontendStack",
    api_endpoint=backend_stack.api_endpoint,
    env=cdk.Environment(
        account=app.node.try_get_context("account"),
        region=app.node.try_get_context("region") or "ap-southeast-2"
    )
)

# Frontend depends on backend
frontend_stack.add_dependency(backend_stack)

app.synth()
