import aws_cdk as cdk
from aws_cdk import (
    Stack,
    aws_lambda as _lambda,
    aws_apigateway as apigateway,
    aws_rds as rds,
    aws_ec2 as ec2,
    aws_dynamodb as dynamodb,
    aws_s3 as s3,
    aws_iam as iam,
    Duration,
    RemovalPolicy,
)
from constructs import Construct


class JerseyParserStack(Stack):
    def __init__(self, scope: Construct, construct_id: str, **kwargs) -> None:
        super().__init__(scope, construct_id, **kwargs)

        # VPC for RDS
        vpc = ec2.Vpc(
            self, "JerseyParserVPC",
            max_azs=2,
            subnet_configuration=[
                ec2.SubnetConfiguration(
                    name="private-subnet",
                    subnet_type=ec2.SubnetType.PRIVATE_WITH_EGRESS,
                    cidr_mask=24,
                ),
                ec2.SubnetConfiguration(
                    name="public-subnet",
                    subnet_type=ec2.SubnetType.PUBLIC,
                    cidr_mask=24,
                ),
            ]
        )

        # RDS PostgreSQL Instance (Aurora Serverless not available in ap-southeast-2)
        db_instance = rds.DatabaseInstance(
            self, "JerseyParserDB",
            engine=rds.DatabaseInstanceEngine.postgres(
                version=rds.PostgresEngineVersion.VER_15
            ),
            instance_type=ec2.InstanceType.of(
                ec2.InstanceClass.T3,
                ec2.InstanceSize.MICRO
            ),
            vpc=vpc,
            vpc_subnets=ec2.SubnetSelection(
                subnet_type=ec2.SubnetType.PRIVATE_WITH_EGRESS
            ),
            database_name="jersey_parser",
            credentials=rds.Credentials.from_generated_secret(
                "jerseydbadmin",
                secret_name="jersey-parser-db-secret"
            ),
            allocated_storage=20,
            storage_type=rds.StorageType.GP2,
            backup_retention=Duration.days(1),
            deletion_protection=False,
            removal_policy=RemovalPolicy.DESTROY,
        )

        # DynamoDB for caching
        cache_table = dynamodb.Table(
            self, "JerseyCacheTable",
            table_name="jersey-cache",
            partition_key=dynamodb.Attribute(
                name="cache_key", type=dynamodb.AttributeType.STRING
            ),
            time_to_live_attribute="ttl",
            billing_mode=dynamodb.BillingMode.PAY_PER_REQUEST,
            removal_policy=RemovalPolicy.DESTROY,
        )

        # S3 bucket for static assets
        assets_bucket = s3.Bucket(
            self, "JerseyAssetsBucket",
            bucket_name=f"jersey-parser-assets-{self.account}-{self.region}",
            cors=[
                s3.CorsRule(
                    allowed_methods=[s3.HttpMethods.GET],
                    allowed_origins=["*"],
                    allowed_headers=["*"],
                )
            ],
            removal_policy=RemovalPolicy.DESTROY,
            auto_delete_objects=True,
        )

        # Lambda Layer for spaCy
        spacy_layer = _lambda.LayerVersion(
            self, "SpacyLayer",
            code=_lambda.Code.from_asset("layer/spacy-layer.zip"),
            compatible_runtimes=[_lambda.Runtime.PYTHON_3_9],
            description="spaCy NLP library with English model",
        )

        # IAM role for Lambda functions
        lambda_role = iam.Role(
            self, "JerseyParserLambdaRole",
            assumed_by=iam.ServicePrincipal("lambda.amazonaws.com"),
            managed_policies=[
                iam.ManagedPolicy.from_aws_managed_policy_name(
                    "service-role/AWSLambdaVPCAccessExecutionRole"
                ),
            ],
        )

        # Grant permissions
        db_instance.secret.grant_read(lambda_role)
        cache_table.grant_read_write_data(lambda_role)
        assets_bucket.grant_read(lambda_role)

        # Lambda function for parsing
        parse_function = _lambda.Function(
            self, "ParseFunction",
            runtime=_lambda.Runtime.PYTHON_3_9,
            handler="lambda_handlers.parse_handler.handler",
            code=_lambda.Code.from_asset("src"),
            layers=[spacy_layer],
            role=lambda_role,
            timeout=Duration.seconds(60),
            memory_size=256,
            environment={
                "DB_HOST": db_instance.instance_endpoint.hostname,
                "DB_PORT": str(db_instance.instance_endpoint.port),
                "DB_NAME": "jersey_parser",
                "DB_SECRET_ARN": db_instance.secret.secret_arn,
                "CACHE_TABLE_NAME": cache_table.table_name,
                "ASSETS_BUCKET_NAME": assets_bucket.bucket_name,
            },
            vpc=vpc,
        )

        # Lambda function for searching
        search_function = _lambda.Function(
            self, "SearchFunction",
            runtime=_lambda.Runtime.PYTHON_3_9,
            handler="lambda_handlers.search_handler.handler",
            code=_lambda.Code.from_asset("src"),
            layers=[spacy_layer],
            role=lambda_role,
            timeout=Duration.seconds(30),
            memory_size=256,
            environment={
                "DB_HOST": db_instance.instance_endpoint.hostname,
                "DB_PORT": str(db_instance.instance_endpoint.port),
                "DB_NAME": "jersey_parser",
                "DB_SECRET_ARN": db_instance.secret.secret_arn,
                "CACHE_TABLE_NAME": cache_table.table_name,
                "ASSETS_BUCKET_NAME": assets_bucket.bucket_name,
            },
            vpc=vpc,
        )

        # API Gateway
        api = apigateway.RestApi(
            self, "JerseyParserAPI",
            rest_api_name="Jersey Parser Service",
            description="API for parsing horse racing jersey descriptions",
            default_cors_preflight_options=apigateway.CorsOptions(
                allow_origins=apigateway.Cors.ALL_ORIGINS,
                allow_methods=apigateway.Cors.ALL_METHODS,
                allow_headers=["Content-Type", "X-Amz-Date", "Authorization"],
            ),
        )

        # API resources
        jerseys = api.root.add_resource("jerseys")
        
        # POST /jerseys/parse
        parse_resource = jerseys.add_resource("parse")
        parse_resource.add_method(
            "POST",
            apigateway.LambdaIntegration(parse_function),
        )

        # GET /jerseys/search
        search_resource = jerseys.add_resource("search")
        search_resource.add_method(
            "GET",
            apigateway.LambdaIntegration(search_function),
        )

        # GET /jerseys/{id}
        jersey_by_id = jerseys.add_resource("{id}")
        jersey_by_id.add_method(
            "GET",
            apigateway.LambdaIntegration(search_function),
        )

        # Store API endpoint for frontend stack reference
        self.api_endpoint = api.url
        
        # Outputs
        cdk.CfnOutput(self, "ApiEndpoint", value=api.url)
        cdk.CfnOutput(self, "DatabaseHost", value=db_instance.instance_endpoint.hostname)
        cdk.CfnOutput(self, "DatabasePort", value=str(db_instance.instance_endpoint.port))
        cdk.CfnOutput(self, "DatabaseSecretArn", value=db_instance.secret.secret_arn)
        cdk.CfnOutput(self, "CacheTableName", value=cache_table.table_name)
        cdk.CfnOutput(self, "AssetsBucketName", value=assets_bucket.bucket_name)
