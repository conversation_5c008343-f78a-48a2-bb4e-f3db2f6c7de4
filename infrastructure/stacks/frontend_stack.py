import os
from urllib.parse import urlparse
from aws_cdk import (
    Stack,
    aws_s3 as s3,
    aws_s3_deployment as s3deploy,
    aws_cloudfront as cloudfront,
    aws_cloudfront_origins as origins,
    aws_iam as iam,
    RemovalPolicy,
    CfnOutput,
    Duration,
    DockerImage,
    BundlingOptions
)
from constructs import Construct

class FrontendStack(Stack):
    """
    CDK Stack for deploying the React/Next.js frontend to AWS.
    Uses S3 for static hosting and CloudFront for global CDN distribution.
    """

    def __init__(self, scope: Construct, construct_id: str, api_endpoint: str, **kwargs) -> None:
        super().__init__(scope, construct_id, **kwargs)

        # Create S3 bucket for static hosting with CloudFront OAI
        frontend_bucket = s3.Bucket(
            self, "FrontendBucket",
            bucket_name=f"jersey-parser-frontend-{self.account}-{self.region}",
            public_read_access=False,  # We'll use CloudFront OAI
            block_public_access=s3.BlockPublicAccess.BLOCK_ALL,
            removal_policy=RemovalPolicy.DESTROY,  # For dev/staging
            auto_delete_objects=True
        )

        # Create Origin Access Identity for CloudFront
        oai = cloudfront.OriginAccessIdentity(
            self, "FrontendOAI",
            comment="OAI for Jersey Parser Frontend"
        )

        # Grant CloudFront access to S3 bucket
        frontend_bucket.grant_read(oai)

        # Create CloudFront distribution
        distribution = cloudfront.Distribution(
            self, "FrontendDistribution",
            default_behavior=cloudfront.BehaviorOptions(
                origin=origins.S3Origin(
                    frontend_bucket,
                    origin_access_identity=oai
                ),
                viewer_protocol_policy=cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
                cache_policy=cloudfront.CachePolicy.CACHING_OPTIMIZED,
                compress=True
            ),
            additional_behaviors={
                "/api/*": cloudfront.BehaviorOptions(
                    origin=origins.HttpOrigin(
                        urlparse(api_endpoint).netloc,
                        origin_path="/prod"
                    ),
                    viewer_protocol_policy=cloudfront.ViewerProtocolPolicy.HTTPS_ONLY,
                    cache_policy=cloudfront.CachePolicy.CACHING_DISABLED,
                    allowed_methods=cloudfront.AllowedMethods.ALLOW_ALL,
                    origin_request_policy=cloudfront.OriginRequestPolicy.CORS_S3_ORIGIN
                )
            },
            default_root_object="index.html",
            comment="Jersey Parser Frontend CDN"
        )

        # Deploy pre-built frontend assets
        # Note: Frontend should be built locally before CDK deployment
        frontend_deployment = s3deploy.BucketDeployment(
            self, "FrontendDeployment",
            sources=[
                s3deploy.Source.asset(
                    os.path.join(os.path.dirname(__file__), "../../jersey-parser-frontend/out")
                )
            ],
            destination_bucket=frontend_bucket,
            distribution=distribution,
            distribution_paths=["/*"]
        )

        # Outputs
        CfnOutput(
            self, "FrontendBucketName",
            value=frontend_bucket.bucket_name,
            description="S3 bucket name for frontend"
        )

        CfnOutput(
            self, "FrontendURL",
            value=f"https://{distribution.distribution_domain_name}",
            description="Frontend URL via CloudFront"
        )

        CfnOutput(
            self, "CloudFrontDistributionId",
            value=distribution.distribution_id,
            description="CloudFront distribution ID"
        )

        # Store references for other stacks
        self.frontend_bucket = frontend_bucket
        self.distribution = distribution
