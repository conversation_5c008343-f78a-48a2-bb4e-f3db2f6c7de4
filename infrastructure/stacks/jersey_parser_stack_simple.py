import aws_cdk as cdk
from aws_cdk import (
    Stack,
    Duration,
    RemovalPolicy,
    CfnOutput,
    aws_lambda as _lambda,
    aws_apigateway as apigateway,
    aws_dynamodb as dynamodb,
    aws_s3 as s3,
    aws_iam as iam,
)
from constructs import Construct


class JerseyParserSimpleStack(Stack):
    def __init__(self, scope: Construct, construct_id: str, **kwargs) -> None:
        super().__init__(scope, construct_id, **kwargs)

        # DynamoDB table for jersey data (simplified approach)
        jersey_table = dynamodb.Table(
            self, "JerseyTable",
            partition_key=dynamodb.Attribute(
                name="id", type=dynamodb.AttributeType.STRING
            ),
            time_to_live_attribute="ttl",
            billing_mode=dynamodb.BillingMode.PAY_PER_REQUEST,
            removal_policy=RemovalPolicy.DESTROY,
        )

        # DynamoDB table for caching
        cache_table = dynamodb.Table(
            self, "JerseyCacheTable",
            partition_key=dynamodb.Attribute(
                name="cache_key", type=dynamodb.AttributeType.STRING
            ),
            time_to_live_attribute="ttl",
            billing_mode=dynamodb.BillingMode.PAY_PER_REQUEST,
            removal_policy=RemovalPolicy.DESTROY,
        )

        # S3 bucket for static assets
        assets_bucket = s3.Bucket(
            self, "JerseyAssetsBucket",
            bucket_name=f"jersey-parser-assets-{self.account}-{self.region}",
            cors=[
                s3.CorsRule(
                    allowed_methods=[s3.HttpMethods.GET],
                    allowed_origins=["*"],
                    allowed_headers=["*"],
                )
            ],
            removal_policy=RemovalPolicy.DESTROY,
            auto_delete_objects=True,
        )

        # IAM role for Lambda functions
        lambda_role = iam.Role(
            self, "JerseyParserLambdaRole",
            assumed_by=iam.ServicePrincipal("lambda.amazonaws.com"),
            managed_policies=[
                iam.ManagedPolicy.from_aws_managed_policy_name(
                    "service-role/AWSLambdaBasicExecutionRole"
                ),
            ],
        )

        # Grant permissions
        jersey_table.grant_read_write_data(lambda_role)
        cache_table.grant_read_write_data(lambda_role)
        assets_bucket.grant_read(lambda_role)

        # Lambda function for parsing (using simplified handler)
        parse_function = _lambda.Function(
            self, "ParseFunction",
            runtime=_lambda.Runtime.PYTHON_3_9,
            handler="lambda_handlers.simple_parse_handler.handler",
            code=_lambda.Code.from_asset("src"),
            role=lambda_role,
            timeout=Duration.seconds(60),
            memory_size=256,
            environment={
                "JERSEY_TABLE_NAME": jersey_table.table_name,
                "CACHE_TABLE_NAME": cache_table.table_name,
                "ASSETS_BUCKET_NAME": assets_bucket.bucket_name,
            },
        )

        # Lambda function for searching
        search_function = _lambda.Function(
            self, "SearchFunction",
            runtime=_lambda.Runtime.PYTHON_3_9,
            handler="lambda_handlers.simple_search_handler.handler",
            code=_lambda.Code.from_asset("src"),
            role=lambda_role,
            timeout=Duration.seconds(30),
            memory_size=256,
            environment={
                "JERSEY_TABLE_NAME": jersey_table.table_name,
                "CACHE_TABLE_NAME": cache_table.table_name,
                "ASSETS_BUCKET_NAME": assets_bucket.bucket_name,
            },
        )

        # API Gateway
        api = apigateway.RestApi(
            self, "JerseyParserAPI",
            rest_api_name="Jersey Parser Service",
            description="API for parsing horse racing jersey descriptions",
            default_cors_preflight_options=apigateway.CorsOptions(
                allow_origins=apigateway.Cors.ALL_ORIGINS,
                allow_methods=apigateway.Cors.ALL_METHODS,
                allow_headers=["Content-Type", "X-Amz-Date", "Authorization", "X-Api-Key"],
            ),
        )

        # API Gateway resources and methods
        jerseys = api.root.add_resource("jerseys")

        # POST /jerseys/parse
        parse_resource = jerseys.add_resource("parse")
        parse_resource.add_method(
            "POST",
            apigateway.LambdaIntegration(parse_function),
        )

        # GET /jerseys/search
        search_resource = jerseys.add_resource("search")
        search_resource.add_method(
            "GET",
            apigateway.LambdaIntegration(search_function),
        )

        # GET /jerseys/{id}
        jersey_by_id = jerseys.add_resource("{id}")
        jersey_by_id.add_method(
            "GET",
            apigateway.LambdaIntegration(search_function),
        )

        # Outputs
        CfnOutput(self, "ApiEndpoint", value=api.url)
        CfnOutput(self, "JerseyTableName", value=jersey_table.table_name)
        CfnOutput(self, "CacheTableName", value=cache_table.table_name)
        CfnOutput(self, "AssetsBucketName", value=assets_bucket.bucket_name)
